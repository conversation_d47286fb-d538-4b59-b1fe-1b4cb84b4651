import os
import google.generativeai as genai
from google.generativeai import types
from PIL import Image
from io import BytesIO
import base64
import textwrap

# --- Configuration ---
# The script will automatically look for the API key in the environment variable.
try:
    genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
except KeyError:
    raise EnvironmentError("Please set the GOOGLE_API_KEY environment variable.")

# Choose the model for image generation.
MODEL_NAME = "gemini-2.0-flash-preview-image-generation"

# --- Main function to generate and save the image ---
def generate_image_from_prompt(prompt, filename="generated_image.png"):
    """
    Generates an image from a text prompt using the Gemini API and saves it to a file.

    Args:
        prompt (str): The text description of the image to generate.
        filename (str): The name of the file to save the image as.
    """
    try:
        # Get a GenerativeModel instance
        model = genai.GenerativeModel(MODEL_NAME)

        # Define the generation configuration with the required modalities
        generation_config = types.GenerationConfig(
            response_modalities=['TEXT', 'IMAGE']
        )

        # Call the generate_content method
        response = model.generate_content(
            prompt,
            generation_config=generation_config
        )

        # Check if the response contains an image
        if response.candidates and response.candidates[0].content:
            for part in response.candidates[0].content.parts:
                if part.inline_data:
                    # The image data is base64 encoded
                    image_bytes = base64.b64decode(part.inline_data.data)
                    image = Image.open(BytesIO(image_bytes))

                    # Save the image
                    image.save(filename)
                    print(f"Image successfully generated and saved as '{filename}'")
                elif part.text:
                    # In some cases, the model might also return text, such as a caption.
                    print(f"Model also returned text: {part.text}")
        else:
            print("No image or a malformed response was generated.")

    except Exception as e:
        print(f"An error occurred: {e}")

# --- Example Usage ---
if __name__ == "__main__":
    # The prompt for the image you want to create
    image_prompt = "A vibrant, photorealistic image of a futuristic cyberpunk city at sunset, with flying cars and holographic advertisements."

    # Run the function
    generate_image_from_prompt(image_prompt)