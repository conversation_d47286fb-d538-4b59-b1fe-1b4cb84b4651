import vertexai
from vertexai.preview.vision_models import ImageGenerationModel
from PIL import Image
import base64
from io import BytesIO

# --- Project and Location Configuration ---
# TODO: Replace with your actual project ID and location
PROJECT_ID = "blissful-sled-467718-j6"  # Example: "my-image-gen-project"
LOCATION = "us-central1" # Make sure this is a supported region for Imagen

# --- Initialization ---
try:
    vertexai.init(project=PROJECT_ID, location=LOCATION)
except Exception as e:
    print(f"Failed to initialize Vertex AI. Error: {e}")
    print("Please check your PROJECT_ID, LOCATION, and gcloud authentication.")
    exit()

# --- Image Generation Function ---
def generate_image(prompt: str, aspect_ratio: str = "1:1", filename: str = "generated_image.png"):
    """
    Generates an image using the Imagen model on Vertex AI.

    Args:
        prompt (str): The text description for the image.
        aspect_ratio (str): The aspect ratio of the image (e.g., "1:1", "16:9").
        filename (str): The name of the file to save the image.
    """
    try:
        # Load the image generation model
        model = ImageGenerationModel.from_pretrained("imagen-3.0-generate-002")

        print(f"Generating image for prompt: '{prompt}'")
        
        # Generate the images with specified parameters
        images = model.generate_images(
            prompt=prompt,
            number_of_images=1,
            aspect_ratio=aspect_ratio,
        )

        # The image data is returned as a PIL Image object
        generated_image = images[0]
        
        # Save the image to a file
        generated_image.save(location=filename)
        print(f"Image saved to {filename}")

    except Exception as e:
        print(f"An error occurred during image generation: {e}")


# --- Example Usage ---
if __name__ == "__main__":
    # The prompt for the image you want to create
    image_prompt = "Photorealistic, natural lighting, 8K detail, cinematic color grading, soft bokeh, slow camera panning. A 25-year-old Indian effeminate man with luminous, porcelain-like fair skin radiating warmth, youthful innocence, and profound peace. His face is meticulously smooth and completely free of any facial hair. His eyes are gently closed in peaceful contentment, and his lips are curled into a soft, genuine smile with a light rose-pink tint, reflecting gentle emotional bonding and calm anticipation. His demeanor conveys a mix of gentle shyness and deep stability, as he feels safe next to a powerfully muscular 45-year-old Indian man he is seated intimately with. Their shoulders are touching, their fingers are gently intertwined, and they are looking deeply into each other's eyes, which conveys deep love and an emotional bond. The younger man has waist-length, skillfully layered, rich chestnut brown or jet-black hair, styled like a heavy, feminine waterfall, with each strand shimmering with a silky sheen. A magnificent golden peacock hairpin with a brilliant ruby cabochon is tucked into his hair. He is adorned in an elegant soft lavender dhoti-kurta made of fine cotton silk, featuring subtle silver thread embroidery (floral, water motifs) and tiny sequin accents. He also wears a thin silver shawl and traditional silver anklets with small bells. The setting is a serene riverside in the peaceful Indian countryside. The golden morning sunlight reflects on the gentle water ripples, creating a tranquil and dreamy atmosphere. Beautiful willow trees filter the sunlight, casting dancing shadows on the ground. The air is filled with the scent of fresh wet earth, river water, and wild flowers, complemented by the slow chirping of birds. The ground is adorned with smooth river stones, soft grass, and wild flowers. --neg distorted, blurry, poor quality, incoherent, extra limbs, cut-off limbs, watermark, text, logo, bad art, bad texture, unrealistic"

    # Generate a square image
    generate_image(image_prompt, aspect_ratio="16:9", filename="cat_square.png")

    # Generate a widescreen image
    # generate_image(image_prompt, aspect_ratio="16:9", filename="cat_widescreen.png")