import vertexai
from vertexai.preview.vision_models import ImageGenerationModel
from PIL import Image, ImageEnhance, ImageFilter
import base64
from io import BytesIO
import os
from datetime import datetime
import json

# --- Project and Location Configuration ---
PROJECT_ID = "blissful-sled-467718-j6"
LOCATION = "us-central1"  # Supported regions: us-central1, us-east4, us-west1, europe-west4

# --- Quality and Style Presets ---
QUALITY_PRESETS = {
    "ultra_high": {
        "model": "imagen-3.0-generate-002",
        "guidance_scale": 15,
        "seed": None,
        "safety_filter_level": "block_some",
        "person_generation": "allow_adult"
    },
    "high": {
        "model": "imagen-3.0-generate-002",
        "guidance_scale": 12,
        "seed": None,
        "safety_filter_level": "block_some",
        "person_generation": "allow_adult"
    },
    "balanced": {
        "model": "imagen-3.0-generate-002",
        "guidance_scale": 8,
        "seed": None,
        "safety_filter_level": "block_some",
        "person_generation": "allow_adult"
    }
}

STYLE_ENHANCERS = {
    "photorealistic": "photorealistic, 8K resolution, ultra-detailed, professional photography, natural lighting, sharp focus, high dynamic range",
    "artistic": "artistic masterpiece, fine art, detailed brushwork, vibrant colors, professional composition",
    "cinematic": "cinematic lighting, film grain, dramatic composition, color grading, depth of field, professional cinematography",
    "portrait": "professional portrait photography, studio lighting, sharp focus on subject, beautiful bokeh background",
    "landscape": "landscape photography, golden hour lighting, wide angle, breathtaking vista, natural beauty"
}

# --- Initialization ---
try:
    vertexai.init(project=PROJECT_ID, location=LOCATION)
    print(f"✅ Successfully initialized Vertex AI (Project: {PROJECT_ID}, Location: {LOCATION})")
except Exception as e:
    print(f"❌ Failed to initialize Vertex AI. Error: {e}")
    print("\n🔧 Troubleshooting steps:")
    print("1. Install Google Cloud CLI: https://cloud.google.com/sdk/docs/install")
    print("2. Authenticate: gcloud auth login")
    print("3. Set application default credentials: gcloud auth application-default login")
    print("4. Verify project ID and location are correct")
    exit()

# --- Enhanced Image Generation Functions ---

def enhance_prompt(prompt: str, style: str = "photorealistic", negative_prompt: str = "") -> str:
    """
    Enhances the user prompt with quality and style modifiers.

    Args:
        prompt (str): Original user prompt
        style (str): Style preset from STYLE_ENHANCERS
        negative_prompt (str): Things to avoid in the image

    Returns:
        str: Enhanced prompt
    """
    style_enhancement = STYLE_ENHANCERS.get(style, STYLE_ENHANCERS["photorealistic"])

    enhanced = f"{style_enhancement}, {prompt}"

    if negative_prompt:
        enhanced += f" --neg {negative_prompt}"

    return enhanced

def generate_high_quality_image(
    prompt: str,
    style: str = "photorealistic",
    quality: str = "ultra_high",
    aspect_ratio: str = "1:1",
    number_of_images: int = 1,
    filename: str = None,
    negative_prompt: str = "blurry, low quality, distorted, watermark, text, logo, bad anatomy, extra limbs, deformed",
    enhance_image: bool = True
):
    """
    Generates high-quality images using advanced Imagen parameters.

    Args:
        prompt (str): The text description for the image
        style (str): Style preset (photorealistic, artistic, cinematic, portrait, landscape)
        quality (str): Quality preset (ultra_high, high, balanced)
        aspect_ratio (str): Image aspect ratio (1:1, 16:9, 9:16, 4:3, 3:4)
        number_of_images (int): Number of images to generate (1-8)
        filename (str): Output filename (auto-generated if None)
        negative_prompt (str): Things to avoid in the image
        enhance_image (bool): Apply post-processing enhancements

    Returns:
        list: List of generated image file paths
    """
    try:
        # Get quality settings
        quality_settings = QUALITY_PRESETS.get(quality, QUALITY_PRESETS["high"])

        # Load the image generation model
        model = ImageGenerationModel.from_pretrained(quality_settings["model"])

        # Enhance the prompt
        enhanced_prompt = enhance_prompt(prompt, style, negative_prompt)

        print(f"🎨 Generating {number_of_images} image(s) with {quality} quality...")
        print(f"📝 Style: {style}")
        print(f"📐 Aspect ratio: {aspect_ratio}")
        print(f"🔍 Enhanced prompt: {enhanced_prompt[:100]}...")

        # Generate the images with advanced parameters
        images = model.generate_images(
            prompt=enhanced_prompt,
            number_of_images=number_of_images,
            aspect_ratio=aspect_ratio,
            guidance_scale=quality_settings["guidance_scale"],
            seed=quality_settings["seed"],
            safety_filter_level=quality_settings["safety_filter_level"],
            person_generation=quality_settings["person_generation"]
        )

        generated_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for i, generated_image in enumerate(images):
            # Generate filename if not provided
            if filename is None:
                base_name = f"imagen_{quality}_{style}_{timestamp}"
                if number_of_images > 1:
                    current_filename = f"{base_name}_{i+1}.png"
                else:
                    current_filename = f"{base_name}.png"
            else:
                if number_of_images > 1:
                    name, ext = os.path.splitext(filename)
                    current_filename = f"{name}_{i+1}{ext}"
                else:
                    current_filename = filename

            # Apply post-processing enhancements if requested
            if enhance_image:
                generated_image = apply_image_enhancements(generated_image)

            # Save the image
            generated_image.save(location=current_filename)
            generated_files.append(current_filename)
            print(f"✅ Image {i+1} saved to: {current_filename}")

        # Save generation metadata
        save_generation_metadata(enhanced_prompt, quality_settings, aspect_ratio, generated_files)

        return generated_files

    except Exception as e:
        print(f"❌ An error occurred during image generation: {e}")
        return []

def apply_image_enhancements(generated_image):
    """
    Apply post-processing enhancements to improve image quality.

    Args:
        generated_image: Vertex AI GeneratedImage object

    Returns:
        Enhanced GeneratedImage object (or original if enhancement fails)
    """
    try:
        # Try to access the PIL image from the GeneratedImage object
        if hasattr(generated_image, '_pil_image') and generated_image._pil_image is not None:
            pil_image = generated_image._pil_image
        elif hasattr(generated_image, 'pil_image'):
            pil_image = generated_image.pil_image
        else:
            # If we can't access the PIL image directly, skip enhancement
            print("⚠️ Info: Skipping post-processing - PIL image not accessible")
            return generated_image

        # Enhance sharpness slightly
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.1)

        # Enhance contrast slightly
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(1.05)

        # Enhance color saturation slightly
        enhancer = ImageEnhance.Color(pil_image)
        pil_image = enhancer.enhance(1.05)

        # Try to update the GeneratedImage object with enhanced PIL image
        if hasattr(generated_image, '_pil_image'):
            try:
                generated_image._pil_image = pil_image
            except AttributeError:
                # If we can't set the attribute, the enhancement is still applied
                # when the image is saved, so this is not a critical error
                pass

        return generated_image
    except Exception as e:
        print(f"⚠️ Warning: Could not apply enhancements: {e}")
        return generated_image

def save_generation_metadata(prompt, settings, aspect_ratio, files):
    """Save metadata about the generation for future reference."""
    try:
        metadata = {
            "timestamp": datetime.now().isoformat(),
            "prompt": prompt,
            "settings": settings,
            "aspect_ratio": aspect_ratio,
            "generated_files": files
        }

        metadata_file = f"generation_metadata_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"📋 Metadata saved to: {metadata_file}")
    except Exception as e:
        print(f"⚠️ Warning: Could not save metadata: {e}")

# --- Convenience Functions ---

def quick_generate(prompt: str, style: str = "photorealistic", filename: str = None):
    """Quick generation with optimal settings for most use cases."""
    return generate_high_quality_image(
        prompt=prompt,
        style=style,
        quality="ultra_high",
        aspect_ratio="1:1",
        filename=filename
    )

def generate_portrait(prompt: str, filename: str = None):
    """Optimized for portrait generation."""
    return generate_high_quality_image(
        prompt=prompt,
        style="portrait",
        quality="ultra_high",
        aspect_ratio="3:4",
        filename=filename,
        negative_prompt="distorted face, extra limbs, blurry, low quality, bad anatomy"
    )

def generate_landscape(prompt: str, filename: str = None):
    """Optimized for landscape generation."""
    return generate_high_quality_image(
        prompt=prompt,
        style="landscape",
        quality="ultra_high",
        aspect_ratio="16:9",
        filename=filename,
        negative_prompt="blurry, low quality, distorted, people, text, watermark"
    )

def generate_artwork(prompt: str, filename: str = None):
    """Optimized for artistic/creative generation."""
    return generate_high_quality_image(
        prompt=prompt,
        style="artistic",
        quality="ultra_high",
        aspect_ratio="1:1",
        filename=filename,
        negative_prompt="photorealistic, photograph, low quality, blurry"
    )

# Legacy function for backward compatibility
def generate_image(prompt: str, aspect_ratio: str = "1:1", filename: str = "generated_image.png"):
    """Legacy function - use generate_high_quality_image for better results."""
    return generate_high_quality_image(
        prompt=prompt,
        aspect_ratio=aspect_ratio,
        filename=filename,
        quality="high"
    )


# --- Example Usage ---
if __name__ == "__main__":
    print("🚀 Enhanced Imagen Image Generator")
    print("=" * 50)

    # Example 1: High-quality portrait
    portrait_prompt = """A 25-year-old Indian man with luminous, fair skin radiating warmth and peace.
    His face is smooth and he has a gentle, genuine smile. He has waist-length, layered chestnut brown hair
    with a silky sheen, adorned with a golden peacock hairpin with ruby. He wears an elegant lavender
    dhoti-kurta with silver embroidery. Setting is a serene riverside in Indian countryside with golden
    morning sunlight, willow trees, and wild flowers."""

    print("\n📸 Generating ultra-high quality portrait...")
    generate_high_quality_image(
        prompt=portrait_prompt,
        style="portrait",
        quality="ultra_high",
        aspect_ratio="3:4",
        filename="enhanced_portrait.png",
        negative_prompt="distorted face, extra limbs, blurry, low quality, watermark, text"
    )

    # Example 2: Cinematic landscape
    landscape_prompt = """Breathtaking mountain landscape at golden hour, snow-capped peaks,
    pristine alpine lake reflecting the mountains, wildflowers in foreground, dramatic clouds"""

    print("\n🏔️ Generating cinematic landscape...")
    generate_high_quality_image(
        prompt=landscape_prompt,
        style="cinematic",
        quality="ultra_high",
        aspect_ratio="16:9",
        filename="cinematic_landscape.png"
    )

    # Example 3: Multiple artistic variations
    art_prompt = """Majestic white tiger in a mystical forest, ethereal lighting, magical atmosphere"""

    print("\n🎨 Generating multiple artistic variations...")
    generate_high_quality_image(
        prompt=art_prompt,
        style="artistic",
        quality="high",
        aspect_ratio="1:1",
        number_of_images=3,
        filename="artistic_tiger.png"
    )

    # Example 4: Using convenience functions
    print("\n🐕 Testing convenience functions...")

    # Quick portrait
    quick_generate("A professional headshot of a confident business woman", "portrait")

    # Landscape
    generate_landscape("Serene mountain lake at sunset with reflection")

    # Artwork
    generate_artwork("Abstract painting of flowing water in blues and greens")

    print("\n✨ Generation complete! Check the generated images and metadata files.")
    print("\n💡 Tips for better results:")
    print("- Use descriptive, detailed prompts")
    print("- Specify lighting, mood, and style preferences")
    print("- Use negative prompts to avoid unwanted elements")
    print("- Experiment with different aspect ratios and styles")