import vertexai
from vertexai.preview.vision_models import ImageGenerationModel
from PIL import Image
import base64
from io import BytesIO

# --- Project and Location Configuration ---
# TODO: Replace with your actual project ID and location
PROJECT_ID = "your-gcp-project-id"  # Example: "my-image-gen-project"
LOCATION = "us-central1" # Make sure this is a supported region for Imagen

# --- Initialization ---
try:
    vertexai.init(project=PROJECT_ID, location=LOCATION)
except Exception as e:
    print(f"Failed to initialize Vertex AI. Error: {e}")
    print("Please check your PROJECT_ID, LOCATION, and gcloud authentication.")
    exit()

# --- Image Generation Function ---
def generate_image(prompt: str, aspect_ratio: str = "1:1", filename: str = "generated_image.png"):
    """
    Generates an image using the Imagen model on Vertex AI.

    Args:
        prompt (str): The text description for the image.
        aspect_ratio (str): The aspect ratio of the image (e.g., "1:1", "16:9").
        filename (str): The name of the file to save the image.
    """
    try:
        # Load the image generation model
        model = ImageGenerationModel.from_pretrained("imagen-3.0-generate-002")

        print(f"Generating image for prompt: '{prompt}'")
        
        # Generate the images with specified parameters
        images = model.generate_images(
            prompt=prompt,
            number_of_images=1,
            aspect_ratio=aspect_ratio,
        )

        # The image data is returned as a PIL Image object
        generated_image = images[0]
        
        # Save the image to a file
        generated_image.save(location=filename)
        print(f"Image saved to {filename}")

    except Exception as e:
        print(f"An error occurred during image generation: {e}")


# --- Example Usage ---
if __name__ == "__main__":
    # The prompt for the image you want to create
    image_prompt = "A hyper-realistic image of a cat wearing a futuristic spacesuit, floating in a nebula."

    # Generate a square image
    generate_image(image_prompt, aspect_ratio="1:1", filename="cat_square.png")

    # Generate a widescreen image
    # generate_image(image_prompt, aspect_ratio="16:9", filename="cat_widescreen.png")